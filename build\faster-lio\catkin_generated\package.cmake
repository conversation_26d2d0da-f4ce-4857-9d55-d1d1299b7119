set(_CATKIN_CURRENT_PACKAGE "faster_lio")
set(faster_lio_VERSION "0.0.0")
set(faster_lio_MAINTAINER "gaoxiang <<EMAIL>>")
set(faster_lio_PACKAGE_FORMAT "1")
set(faster_lio_BUILD_DEPENDS "geometry_msgs" "nav_msgs" "roscpp" "rospy" "std_msgs" "sensor_msgs" "tf" "pcl_ros" "message_generation" "libgoogle-glog-dev" "livox_ros_driver2")
set(faster_lio_BUILD_EXPORT_DEPENDS "geometry_msgs" "nav_msgs" "sensor_msgs" "roscpp" "rospy" "std_msgs" "tf" "pcl_ros" "message_runtime" "libgoogle-glog-dev")
set(faster_lio_BUILDTOOL_DEPENDS "catkin")
set(faster_lio_BUILDTOOL_EXPORT_DEPENDS )
set(faster_lio_EXEC_DEPENDS "geometry_msgs" "nav_msgs" "sensor_msgs" "roscpp" "rospy" "std_msgs" "tf" "pcl_ros" "message_runtime" "libgoogle-glog-dev")
set(faster_lio_RUN_DEPENDS "geometry_msgs" "nav_msgs" "sensor_msgs" "roscpp" "rospy" "std_msgs" "tf" "pcl_ros" "message_runtime" "libgoogle-glog-dev")
set(faster_lio_TEST_DEPENDS "rostest" "rosbag")
set(faster_lio_DOC_DEPENDS )
set(faster_lio_URL_WEBSITE "")
set(faster_lio_URL_BUGTRACKER "")
set(faster_lio_URL_REPOSITORY "")
set(faster_lio_DEPRECATED "")