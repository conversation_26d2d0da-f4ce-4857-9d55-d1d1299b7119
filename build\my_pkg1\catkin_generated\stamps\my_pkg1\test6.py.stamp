#!/usr/bin/env python
# -*- coding: utf-8 -*-
import rospy
import math
import serial
import time
import threading
from geometry_msgs.msg import PointStamped
from std_msgs.msg import Float64
import glob

def find_usb_serial_port():
    ports = glob.glob("/dev/ttyUSB*")
    if not ports:
        rospy.logerr("没有找到任何 /dev/ttyUSB* 设备")
        return None
    return ports[0]  # 只取第一个


class UARTSenderReceiver:
    def __init__(self, baud=115200):
        self.baud = baud
        self.ser = None
        self.running = True
        self.port_in_use = None # 记录当前使用的串口端口
        self.reconnect_attempt_delay = 1.0 # 重新连接尝试的延迟时间

        self.connect_serial() # 首次连接

        self.thread = threading.Thread(target=self.recv_thread)
        self.thread.daemon = True
        self.thread.start()

    def connect_serial(self):
        """尝试查找并连接串口"""
        if self.ser and self.ser.isOpen():
            self.ser.close() # 如果已打开，先关闭
            self.ser = None

        rospy.loginfo("[UART] 尝试连接串口...")
        new_port = find_usb_serial_port()

        if new_port is None:
            rospy.logwarn("[UART] 未找到 /dev/ttyUSB* 设备，将稍后重试。")
            self.ser = None # 确保ser为None
            self.port_in_use = None
            return False

        if new_port != self.port_in_use:
            rospy.loginfo(f"[UART] 发现新串口设备：{new_port}")
            self.port_in_use = new_port
        else:
            rospy.loginfo(f"[UART] 尝试重新连接到 {new_port}。")

        try:
            self.ser = serial.Serial(self.port_in_use, self.baud, timeout=0.1)
            time.sleep(2) # 给串口一点时间初始化
            if self.ser.isOpen():
                rospy.loginfo("[UART] 串口已成功打开: %s", self.port_in_use)
                return True
        except serial.SerialException as e:
            rospy.logerr(f"[UART] 串口 {self.port_in_use} 打开失败: {e}，将稍后重试。")
            self.ser = None
            self.port_in_use = None
            return False
        return False # 如果代码运行到这里，说明没有成功打开

    def send_angle_hex(self, V_y_horizontal, V_y_vertical, xy_distance):
        if self.ser is None or not self.ser.isOpen():
            rospy.logwarn("[UART] 串口未打开或已关闭，尝试重新连接...")
            if not self.connect_serial():
                rospy.logerr("[UART] 重新连接串口失败，无法发送数据。")
                return

        dir_x = 1 if V_y_horizontal >= 0 else 0
        horizontal_value = int(abs(V_y_horizontal))
        dir_y = 1 if V_y_vertical >= 0 else 0
        vertical_value = int(abs(V_y_vertical))

        extra_flag = 0
        # 新增一位，根据xy_distance判断
        if xy_distance is not None:
            if xy_distance < 3.587:
                extra_flag = 1
            elif  3.587 <= xy_distance < 4.068:
                extra_flag = 2
            elif 4.077 <= xy_distance < 4.339:
                extra_flag = 3
            
        else:
            extra_flag = 0  # 可选扩展：超出已知范围


        rospy.loginfo("[UART] PID输出: V_y_horizontal=%.2f, V_y_vertical=%.2f, dir_x=%d, dir_y=%d, extra_flag=%s",
                      V_y_horizontal, V_y_vertical, dir_x, dir_y, extra_flag)

        data = ''.join([
            f"{dir_x:02x}",
            f"{horizontal_value:04x}",
            f"{dir_y:02x}",
            f"{vertical_value:04x}",
            f"{extra_flag:02x}", # 新增一位
            "0d",
            "0a"
        ])

        try:
            byte_data = bytes.fromhex(data)
            self.ser.write(byte_data)
            time.sleep(0.5)
            self.ser.flush()
            rospy.loginfo("[UART] 发送十六进制: %s", data.upper())
        except serial.SerialException as e:
            rospy.logerr(f"[UART] 发送失败，串口可能已断开: {e}。尝试重新连接...")
            self.ser = None
            self.port_in_use = None
        except Exception as e:
            rospy.logerr(f"[UART] 发送时发生未知错误: {e}")

    def recv_thread(self):
        while self.running:
            if self.ser is None or not self.ser.isOpen():
                # 如果串口未打开，尝试重新连接
                if not self.connect_serial():
                    # 如果重连失败，等待一段时间再试，避免无限循环占用CPU
                    time.sleep(self.reconnect_attempt_delay)
                    continue # 继续循环，等待下一次重连尝试

            try:
                # readline() 在timeout后会返回空字符串，不会抛异常
                data = self.ser.readline().decode('utf-8', errors='ignore').strip()
                if data:
                    rospy.loginfo("[UART] 接收: %s", data)
            except serial.SerialException as e: # 捕获串口通信错误
                rospy.logwarn(f"[UART] 接收失败，串口可能已断开: {e}。尝试重新连接...")
                self.ser = None # 强制将串口设为None
                self.port_in_use = None
            
    def close(self):
        self.running = False
        time.sleep(0.2) # 给予接收线程一些时间停止
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=1.0) # 等待线程结束
            if self.thread.is_alive():
                rospy.logwarn("[UART] 接收线程未能及时停止。")

        if self.ser and self.ser.isOpen():
            self.ser.close()
            rospy.loginfo("[UART] 串口已关闭")

class AngleProcessor:
    def __init__(self):
        rospy.init_node('object_uart_hex_node', anonymous=True)
        self.vy_horizontal_pub = rospy.Publisher('/V_y_horizontal', Float64, queue_size=10)
        self.vy_vertical_pub = rospy.Publisher('/V_y_vertical', Float64, queue_size=10)
        self.uart = UARTSenderReceiver()
        self.fire_angle = 0.0  # 初始化fire_angle变量
        self.current_angle = 0.0
        self.angle = 0.0 # 新增self.angle变量
        self.xy_distance = 0.0

  # 标记是否已经发送过

        # PID控制开关
        self.use_horizontal_pid = True
        self.use_vertical_pid = True

        self.kp = 17
        self.ki = 0
        self.kd = 7.9
        self.prev_error = 0.0
        self.integral = 0.0
        self.min_speed = 0  # m/s
        self.max_speed = 3000  # m/s

        # 订阅话题 - 设置小的队列大小以获取最新数据
        rospy.Subscriber("/y_angle", Float64, self.callback, queue_size=1)
        rospy.Subscriber("/fire_angle", Float64, self.fire_angle_callback, queue_size=1)
        rospy.Subscriber("/current_angle", Float64, self.current_angle_callback, queue_size=1)
        rospy.Subscriber("/xy_distance", Float64, self.distance_callback, queue_size=1)
        rospy.on_shutdown(self.shutdown_hook)

        

    def pid_control(self, angle):
        """
        第一个 PID 控制算法 - 用于水平角度控制
        Args:
            angle: 当前角度（度）
        Returns:
            V_y: 控制输出速度
        """
        error = angle  # 目标为0度
        self.integral += error
        derivative = error - self.prev_error
        pid_output = self.kp * error + self.ki * self.integral + self.kd * derivative
        pid_output = min(pid_output, self.max_speed)  # 限制 PID 输出在0~100
        V_y = pid_output  # 角度越大速度越大，角度为0时速度为0
        self.prev_error = error
        return V_y
    """
    分阶段控制垂直速度：
    - 误差大于2时，V_y_vertical=10000
    - 误差在0.2~2之间时，V_y_vertical=100
    - 误差小于0.2时，V_y_vertical=0
    """
    def control_vertical(self, fire_angle, current_angle):
   
        error = abs(fire_angle - current_angle)
        if error > 2:
            V_y_vertical = (fire_angle - current_angle)*500
        elif error >= 0.2:
            V_y_vertical = fire_angle-current_angle
        else:
            V_y_vertical = 0
        rospy.loginfo("垂直速度阶段控制: error=%.3f, V_y_vertical=%.2f", error, V_y_vertical)
        return V_y_vertical


    def fire_angle_callback(self, msg):
        """处理/fire_angle话题的回调函数"""
        current_time = rospy.get_time()
        received_angle = msg.data  # 先保存接收到的值
        self.fire_angle = received_angle
        
        #rospy.loginfo("接收到fire_angle: %.6f ",
                     #self.fire_angle)

    def current_angle_callback(self, msg):
        """处理/current_angle话题的回调函数"""
        current_time = rospy.get_time()
        received_angle = msg.data  # 先保存接收到的值
        self.current_angle = received_angle
        rospy.loginfo("接收到current_angle: %.6f",
                     self.current_angle)

    def distance_callback(self, msg):

        self.xy_distance = msg.data
        rospy.loginfo("接收到/distance: %.3f", msg.data)

    def callback(self, msg):
        current_time = rospy.get_time()
        received_angle = msg.data  # 先保存接收到的值
        self.angle = received_angle

        # rospy.loginfo("=== 角度调试信息 ===")
        # rospy.loginfo("当前时间: %.3f", current_time)
        # rospy.loginfo("msg.data: %.6f", msg.data)
        # rospy.loginfo("received_angle: %.6f", received_angle)
        # rospy.loginfo("self.angle: %.6f", self.angle)
        # rospy.loginfo("==================")

        # 初始化 PID 输出
        V_y_horizontal = 0.0
        V_y_vertical = 0.0

        # 使用第一个 PID 控制算法计算水平速度
        rospy.loginfo("准备调用PID控制，输入角度: %.6f", self.angle)
        V_y_horizontal = self.pid_control(self.angle)
        rospy.loginfo("PID控制输出: %.6f", V_y_horizontal)
        
        # 使用第二个 PID 控制算法计算垂直速度
       
        V_y_vertical = self.control_vertical(self.fire_angle, self.current_angle)
          
        rospy.loginfo("垂直偏移速度: %.2f°", V_y_vertical)
        # 组合 PID 输出
        self.vy_horizontal_pub.publish(Float64(V_y_horizontal))
        self.vy_vertical_pub.publish(Float64(V_y_vertical))
        # 发送 PID 控制结果

        # V_y_vertical=0
        # V_y_horizontal=0 
        if 2<V_y_horizontal<20:
            V_y_horizontal=32
            if -0.05<self.angle<0:
                V_y_horizontal=0
        elif -20<V_y_horizontal<-2:
            V_y_horizontal=-32
            if 0<self.angle<0.05:
                V_y_horizontal=0

        
        rospy.loginfo("水平偏移速度: %.2f°", V_y_horizontal)


        self.uart.send_angle_hex(V_y_horizontal, V_y_vertical, self.xy_distance)
        
    def shutdown_hook(self):
        self.uart.close()

    def run(self):
        rospy.spin()

if __name__ == "__main__":
    try:
        node = AngleProcessor()
        node.run()
    except rospy.ROSInterruptException:
        pass
