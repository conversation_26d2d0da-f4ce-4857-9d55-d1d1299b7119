#!/bin/bash

# 彩色输出定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 显示菜单函数
show_menu() {
    clear
    echo -e "${YELLOW}====== 机器人控制菜单 ======${NC}"
    echo -e "1. ${GREEN}启动雷达${NC}"
    echo -e "2. ${GREEN}启动建图${NC}"
    echo -e "3. ${GREEN}启动定位${NC}"
    echo -e "4. ${GREEN}标记篮筐${NC}"
    echo -e "0. ${RED}退出脚本${NC}"
    echo -e "${YELLOW}===========================${NC}"
}

# 执行对应功能
execute_command() {
    case $1 in
        1)
            echo -e "${GREEN}正在启动雷达...${NC}"
            # 这里替换为实际的雷达启动命令
            roslaunch livox_ros_driver2 msg_MID360.launch 
            ;;
        2)
            echo -e "${GREEN}正在启动定位...${NC}"
            # 这里替换为实际的定位启动命令
            roslaunch faster_lio mapping_avia.launch relocalization_enable:=0  pcd_save_en:=0
            ;;

        3)
            echo -e "${GREEN}正在计算角度篮筐...${NC}"
            # 这里替换为实际的篮筐标记命令
            rosrun faster_lio basket_calculator.py
            ;;

        4)
            echo -e "${GREEN}串口发送...${NC}"
            # 这里替换为实际的篮筐标记命令
            rosrun my_pkg1 test6.py
            ;;

        5)
            echo -e "${GREEN}正在标记篮筐...${NC}"
            # 这里替换为实际的篮筐标记命令
            rosrun basketball_marker marker_node
            ;;
        6)
            echo -e "${GREEN}正在启动建图...${NC}"
            # 这里替换为实际的建图启动命令
            # roslaunch faster_lio mapping_avia.launch relocalization_enable:=0
            ;;

            
        0)
            echo -e "${RED}退出脚本${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}无效选项，请重新输入${NC}"
            ;;
    esac
}

# 主循环
while true; do
    show_menu
    read -p "请输入选项数字 (0-6): " choice
    execute_command $choice
    read -p "按回车键继续..."
done