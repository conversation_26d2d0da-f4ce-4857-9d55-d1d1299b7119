# generated from genmsg/cmake/pkg-genmsg.context.in

messages_str = "/home/<USER>/r2_2/src/faster-lio/msg/Pose6D.msg"
services_str = ""
pkg_name = "faster_lio"
dependencies_str = "geometry_msgs"
langs = "gencpp;geneus;genlisp;gennodejs;genpy"
dep_include_paths_str = "faster_lio;/home/<USER>/r2_2/src/faster-lio/msg;geometry_msgs;/opt/ros/noetic/share/geometry_msgs/cmake/../msg;std_msgs;/opt/ros/noetic/share/std_msgs/cmake/../msg"
PYTHON_EXECUTABLE = "/usr/bin/python3"
package_has_static_sources = '' == 'TRUE'
genmsg_check_deps_script = "/opt/ros/noetic/share/genmsg/cmake/../../../lib/genmsg/genmsg_check_deps.py"
